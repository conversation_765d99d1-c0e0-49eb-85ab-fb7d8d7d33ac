<script lang="ts">
	import WbsTreeItem from './wbs-tree-item.svelte';
	import CaretDownIcon from 'phosphor-svelte/lib/CaretDown';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';
	import type { Snippet } from 'svelte';
	import type { WbsItemTree } from '$lib/schemas/wbs';
	import { enhance } from '$app/forms';
	import { invalidate } from '$app/navigation';
	import type { getBudgetLineItems, getSnapshotLineItems } from '$lib/project_utils';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import { DotsThreeOutlineVertical as DotsThreeOutlineVerticalIcon } from 'phosphor-svelte';

	let {
		item,
		depth = 0,
		showScope = false,
		expandedCategories = $bindable(),
		getBudgetItems = undefined,
		getSnapshotItems = undefined,
		selectedSnapshotId = undefined,
		formatCurrency = undefined,
		calculateTotalCost = undefined,
		calculateSnapshotTotalCost = undefined,
		additionalColumns = undefined,
		canEditProject = false,
		projectPath = '',
		projectId = '',
	}: {
		item: WbsItemTree;
		depth?: number;
		showScope?: boolean;
		expandedCategories: Record<number, boolean>;
		getBudgetItems?: (wbsId: number) => Awaited<ReturnType<typeof getBudgetLineItems>>;
		getSnapshotItems?: (wbsId: number) => Awaited<ReturnType<typeof getSnapshotLineItems>>;
		selectedSnapshotId?: number | null;
		formatCurrency?: (value: number) => string;
		calculateTotalCost?: (items: Awaited<ReturnType<typeof getBudgetLineItems>>) => number;
		calculateSnapshotTotalCost?: (wbsId: number) => number;
		additionalColumns?: Snippet;
		canEditProject?: boolean;
		projectPath?: string;
		projectId?: string;
	} = $props();

	function toggleCategory(id: number) {
		if (expandedCategories[id] === undefined) {
			expandedCategories[id] = false;
		} else {
			expandedCategories[id] = !expandedCategories[id];
		}
	}

	function addLineBreaks(text: string) {
		const output = text.replace(/\\n/g, '<br>');
		return output;
	}

	const budgetItems = $derived.by(() => {
		if (getBudgetItems) {
			return getBudgetItems(item.wbs_library_item_id);
		}
		return [];
	});

	const budgetSnapshotItems = $derived.by(() => {
		if (getSnapshotItems) {
			return getSnapshotItems(item.wbs_library_item_id);
		}
		return [];
	});
</script>

<tr class="hover:bg-gray-50">
	<td class="px-3 py-4 text-sm font-medium whitespace-nowrap">
		<div class="flex items-center">
			{#if item.children.length > 0}
				<button
					type="button"
					class="mr-2 focus:outline-hidden"
					onclick={() => toggleCategory(item.wbs_library_item_id)}
				>
					{#if expandedCategories[item.wbs_library_item_id] ?? true}
						<CaretDownIcon class="size-4 text-gray-500" />
					{:else}
						<CaretRightIcon class="size-4 text-gray-500" />
					{/if}
				</button>
			{:else}
				<div class="mr-6"></div>
			{/if}
			<span>{item.code}</span>
		</div>
	</td>
	<td class="max-w-40 px-6 py-4 text-sm">{item.description || 'No description'}</td>
	{#if showScope}
		<!-- eslint-disable-next-line svelte/no-at-html-tags -->
		<td class="px-6 py-4 text-sm">{@html addLineBreaks(item.cost_scope || '')}</td>
	{/if}

	<!-- If budget functions are provided, render the cost column directly -->
	{#if formatCurrency && getBudgetItems}
		<!-- Quantity Column -->
		<td class="px-6 py-4 text-sm whitespace-nowrap">
			{#if selectedSnapshotId && getSnapshotItems}
				{#each budgetSnapshotItems as budgetItem, index (budgetItem.budget_snapshot_line_item_id)}
					{#if index > 0}<div class="mt-1 border-t pt-1"></div>{/if}
					<div>
						{budgetItem.quantity || ''}
						{budgetItem.unit || ''}
					</div>
				{/each}
			{:else}
				{#each budgetItems as budgetItem, index (budgetItem.budget_line_item_id)}
					{#if index > 0}<div class="mt-1 border-t pt-1"></div>{/if}
					<div>
						{budgetItem.quantity || ''}
						{budgetItem.unit || ''}
					</div>
				{/each}
			{/if}
		</td>

		<!-- Rate Column -->
		<td class="px-6 py-4 text-sm whitespace-nowrap">
			{#if selectedSnapshotId && getSnapshotItems}
				{#each budgetSnapshotItems as budgetItem, index (budgetItem.budget_snapshot_line_item_id)}
					{#if index > 0}<div class="mt-1 border-t pt-1"></div>{/if}
					<div>
						{formatCurrency(budgetItem.unit_rate ?? 0)} / {budgetItem.unit || ''}
					</div>
				{/each}
			{:else}
				{#each budgetItems as budgetItem, index (budgetItem.budget_line_item_id)}
					{#if index > 0}<div class="mt-1 border-t pt-1"></div>{/if}
					<div>
						{formatCurrency(budgetItem.unit_rate ?? 0)} / {budgetItem.unit || ''}
					</div>
				{/each}
			{/if}
		</td>

		<!-- Cost Column - now handled directly in the component -->
		<td class="px-6 py-4 text-sm font-medium whitespace-nowrap">
			{#if selectedSnapshotId && getSnapshotItems}
				{#each budgetSnapshotItems as budgetItem, index (budgetItem.budget_snapshot_line_item_id)}
					{#if index > 0}<div class="mt-1 border-t pt-1"></div>{/if}
					<div class="flex items-center gap-1">
						<span
							>{formatCurrency((budgetItem.quantity ?? 0) * (budgetItem.unit_rate ?? 0))} / {budgetItem.unit ||
								''}</span
						>
						{#if budgetItem.unit_rate_manual_override}
							<span class="ml-1 text-xs text-blue-600">(manual)</span>
						{/if}
					</div>
				{/each}
				{#if budgetSnapshotItems.length > 1 && calculateSnapshotTotalCost}
					<div class="mt-2 border-t pt-1 text-xs font-semibold">
						Total: {formatCurrency(calculateSnapshotTotalCost(item.wbs_library_item_id))}
					</div>
				{/if}
			{:else if item.children.length > 0 && budgetItems.length === 0}
				<!-- Parent item with only children costs (no direct costs) -->
				{formatCurrency(item.totalCost || 0)}
				<span class="ml-1 text-xs text-gray-600">(total from children)</span>
			{:else if item.children.length > 0 && budgetItems.length > 0}
				<!-- Parent item with both direct and children costs -->
				<!-- First show direct costs -->
				{#each budgetItems as budgetItem, index (budgetItem.budget_line_item_id)}
					{#if index > 0}<div class="mt-1 border-t pt-1"></div>{/if}
					<div class="flex items-center gap-1">
						<span>{formatCurrency((budgetItem.quantity ?? 0) * (budgetItem.unit_rate ?? 0))}</span>
						{#if budgetItem.unit_rate_manual_override}
							<span class="ml-1 text-xs text-blue-600">(manual)</span>
						{/if}
					</div>
				{/each}

				<!-- Then show total including children -->
				<div class="mt-2 border-t pt-1 font-semibold">
					Total: {formatCurrency(item.totalCost || 0)}
					<span class="ml-1 text-xs text-gray-600">(incl. children)</span>
				</div>
			{:else}
				<!-- Display the direct cost for leaf items -->
				{#each budgetItems as budgetItem, index (budgetItem.budget_line_item_id)}
					{#if index > 0}<div class="mt-1 border-t pt-1"></div>{/if}
					<div class="flex items-center gap-1">
						<span>{formatCurrency((budgetItem.quantity ?? 0) * (budgetItem.unit_rate ?? 0))}</span>
						{#if budgetItem.unit_rate_manual_override}
							<span class="ml-1 text-xs text-blue-600">(manual)</span>
						{/if}
					</div>
				{/each}
				{#if budgetItems.length > 1 && calculateTotalCost}
					<div class="mt-2 border-t pt-1 text-xs font-semibold">
						Total: {formatCurrency(calculateTotalCost(budgetItems))}
					</div>
				{/if}
			{/if}
		</td>
	{:else if additionalColumns}
		{@render additionalColumns?.()}
	{/if}

	{#if !selectedSnapshotId && canEditProject && getBudgetItems && formatCurrency}
		<td class="px-3 py-4 text-sm whitespace-nowrap">
			<div class="flex flex-col">
				{#each budgetItems as budgetItem, budgetIndex (budgetItem.budget_line_item_id)}
					<div class="flex justify-end px-1">
						<Popover.Root>
							<Popover.Trigger class="p-2">
								<DotsThreeOutlineVerticalIcon class="size-4" />
							</Popover.Trigger>
							<Popover.Content class="w-56" align="end" sideOffset={5}>
								<div class="p-2">
									<a
										href="{projectPath}/edit?id={encodeURIComponent(
											budgetItem.budget_line_item_id,
										)}"
										class="inline-block w-full"
									>
										<button
											type="button"
											class="border-input bg-background ring-offset-background inline-flex h-8 w-full items-center justify-center rounded-md border px-3 text-xs hover:bg-blue-50"
										>
											Edit {budgetItems.length > 1 ? `Item ${budgetIndex + 1}` : ''}
										</button>
									</a>
									<form
										method="POST"
										action="?/deleteBudgetItem"
										use:enhance={() => {
											return async ({ update }) => {
												await update();
												invalidate('project:budget');
											};
										}}
										class="mt-2 w-full"
									>
										<input type="hidden" name="project_id" value={projectId} />
										<input
											type="hidden"
											name="wbs_library_item_id"
											value={item.wbs_library_item_id}
										/>
										<input
											type="hidden"
											name="budget_line_item_id"
											value={budgetItem.budget_line_item_id}
										/>
										<button
											type="submit"
											class="border-input bg-background ring-offset-background inline-flex h-8 w-full items-center justify-center rounded-md border px-3 text-xs text-red-600 hover:bg-red-50"
										>
											Delete {budgetItems.length > 1 ? `Item ${budgetIndex + 1}` : ''}
										</button>
									</form>
								</div>
							</Popover.Content>
						</Popover.Root>
					</div>
				{/each}
			</div>
		</td>
	{/if}
</tr>

{#if (expandedCategories[item.wbs_library_item_id] ?? true) && item.children.length > 0}
	{#each item.children as child (child.wbs_library_item_id)}
		<WbsTreeItem
			item={child}
			depth={depth + 1}
			{showScope}
			bind:expandedCategories
			{getBudgetItems}
			{getSnapshotItems}
			{selectedSnapshotId}
			{formatCurrency}
			{calculateTotalCost}
			{calculateSnapshotTotalCost}
			{additionalColumns}
			{canEditProject}
			{projectPath}
			{projectId}
		/>
	{/each}
{/if}
